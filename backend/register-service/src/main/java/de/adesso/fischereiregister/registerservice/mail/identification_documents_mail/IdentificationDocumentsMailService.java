package de.adesso.fischereiregister.registerservice.mail.identification_documents_mail;

import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.mail.enums.MailTemplate;

import java.util.List;
import java.util.UUID;

/**
 * Handles the creation and dispatch of emails related to Identification Document operations.
 */
public interface IdentificationDocumentsMailService {

    /**
     * Generates and sends an email for Identification Document use cases.
     *
     * @param registerId   Unique identifier of the register entry.
     * @param documentIds  List of attached document IDs.
     * @param to           Recipient's email address.
     * @param mailTemplate Email template type.
     * @param userDetails  Details of the user (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) initiating the request.
     */
    void createAndSendMail(
            UUID registerId,
            List<String> documentIds,
            String to,
            MailTemplate mailTemplate,
            UserDetails userDetails
    );

}
