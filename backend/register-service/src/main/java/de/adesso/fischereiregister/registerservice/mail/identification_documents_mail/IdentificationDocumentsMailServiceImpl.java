package de.adesso.fischereiregister.registerservice.mail.identification_documents_mail;

import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.RegisterEntry;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.user.UserDetails;
import de.adesso.fischereiregister.registerservice.fishing_license_export.FishingLicenseExportService;
import de.adesso.fischereiregister.registerservice.fishing_license_export.model.RenderedContent;
import de.adesso.fischereiregister.registerservice.mail.MailService;
import de.adesso.fischereiregister.registerservice.mail.MailTemplateResolutionServiceImpl;
import de.adesso.fischereiregister.registerservice.mail.enums.MailTemplate;
import de.adesso.fischereiregister.registerservice.register_entry_view.RegisterEntryViewService;
import de.adesso.fischereiregister.registerservice.tenant.TenantConfigurationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class IdentificationDocumentsMailServiceImpl implements IdentificationDocumentsMailService {

    private final TenantConfigurationService tenantConfigurationService;
    private final MailTemplateResolutionServiceImpl mailTemplateResolutionService;
    private final FishingLicenseExportService fishingLicenseExportService;
    private final RegisterEntryViewService registerEntryViewService;
    private final MailService mailService;

    @Override
    public void createAndSendMail(
            UUID registerId,
            List<String> documentIds,
            String to,
            MailTemplate mailTemplate,
            UserDetails userDetails
    ) {
        final FederalState federalState = FederalState.valueOf(userDetails.getFederalState());
        final RegisterEntry registerEntry = registerEntryViewService.findByRegisterId(registerId).getData();
        final Person person = registerEntry.getPerson();

        final String fromAddress = tenantConfigurationService.getValue(federalState, "mail.from_address");

        final String mailSubject = mailTemplateResolutionService.getSubject(federalState, mailTemplate);
        final String mailText = mailTemplateResolutionService.getText(federalState, mailTemplate, person);

        final List<RenderedContent> attachments = exportAttachments(documentIds, registerEntry);

        mailService.sendMail(
                to,
                fromAddress,
                mailSubject,
                mailText,
                attachments
        );
    }

    private List<RenderedContent> exportAttachments(List<String> documentIds, RegisterEntry registerEntry) {
        final List<IdentificationDocument> identificationDocuments = registerEntry.getIdentificationDocuments()
                .stream()
                .filter(document -> documentIds.contains(document.getDocumentId()))
                .toList();

        final List<RenderedContent> fishingLicenseExportAttachments = identificationDocuments.stream()
                .filter(document -> document.getFishingLicense() != null)
                .map(document -> fishingLicenseExportService.exportFishingLicense(registerEntry.getRegisterId(), document.getDocumentId()))
                .toList();

        final List<RenderedContent> fishingTaxExportAttachments = identificationDocuments.stream()
                .filter(document -> document.getTax() != null)
                .map(document -> fishingLicenseExportService.exportFishingTaxDocument(registerEntry.getRegisterId(), document.getDocumentId()))
                .toList();

        final List<RenderedContent> attachments = new ArrayList<>();
        attachments.addAll(fishingTaxExportAttachments);
        attachments.addAll(fishingLicenseExportAttachments);
        return attachments;
    }
}
