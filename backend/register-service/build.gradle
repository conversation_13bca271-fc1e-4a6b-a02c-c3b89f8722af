buildscript {
    dependencies {
        classpath "org.springframework.boot:spring-boot-gradle-plugin:3.4.6"
    }
}

plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.6'
    id 'io.spring.dependency-management' version '1.1.6'
    id 'org.cyclonedx.bom' version '1.10.0'
}

group = 'de.adesso.fischereiregister'
version = '0.18.0-SNAPSHOT'

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
    all {
        exclude group: "commons-logging", module: "commons-logging"
    }
    compileClasspath {
        resolutionStrategy.activateDependencyLocking()
    }
}
def secretsFile = file('gradle-secrets.properties')
def artifactoryUser = System.getenv("ARTIFACTORY_INT_USER")
def artifactoryToken = System.getenv("ARTIFACTORY_INT_TOKEN")

if (secretsFile.exists()) {
    def secrets = new Properties()
    secretsFile.withInputStream { stream ->
        secrets.load(stream)
    }
    secrets.each { key, value ->
        project.ext.set(key, value)
    }
    artifactoryUser = project.ext.username
    artifactoryToken = project.ext.token
}

repositories {
    mavenLocal()
    maven {
        url 'https://artifactory.adesso-group.com/artifactory/fischereiregister-maven-release'
        credentials {
            username = artifactoryUser
            password = artifactoryToken
        }
    }
    maven {
        url 'https://repo-ex.zcdi.dataport.de:443/artifactory/digifischdok-mvn-dev'
        credentials {
            username = System.getenv("MAVEN_SDK_REGISTRY_USER_ID")
            password = System.getenv("MAVEN_SDK_REGISTRY_TOKEN")
        }
    }
    maven {
        url 'https://common-gradle-gradle-remote.repo-in.zcdi.dataport.de/artifactory/common-gradle-gradle-remote'
        credentials {
            username = System.getenv("GRADLE_REGISTRY_USER_ID")
            password = System.getenv("GRADLE_REGISTRY_TOKEN")
        }
    }
    mavenCentral()
    maven { url 'https://mvnrepository.com' }
}

dependencies {
    // IMPORTANT: When adding new modules, they must be added to the copy step of the Dockerfile as well.
    // This is currently a limitation when building with Kaniko and a better workaround has yet to be found.
    implementation project(':core')
    implementation project(':common')
    implementation project(':views:register_data')
    implementation project(':views:search')
    implementation project(':views:ban_expiration')
    implementation project(':views:licenses_statistics')
    implementation project(':views:taxes_statistics')
    implementation project(':views:fees_statistics')
    implementation project(':views:bans_statistics')
    implementation project(':views:certifications_statistics')
    implementation project(':views:register_entry_search')
    implementation project(':message-service')
    implementation project(':card_orders')
    implementation project(':migrations')
    implementation project(':inspector-protocol')

    testImplementation project(':testutils')

    implementation "org.keycloak:keycloak-spring-security-adapter:25.0.3"
    implementation "org.springframework.boot:spring-boot-starter-oauth2-client:3.4.6"
    implementation("org.springframework.boot:spring-boot-starter-security:3.4.6")
    implementation "org.springframework.boot:spring-boot-starter-oauth2-resource-server:3.4.6"
    implementation 'org.mapstruct:mapstruct:1.5.5.Final'
    implementation platform('org.axonframework:axon-bom:4.10.4')
    implementation "org.axonframework:axon-spring-boot-starter:4.11.2"
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.liquibase:liquibase-core:4.28.0'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.5.0'
    implementation 'org.springframework.boot:spring-boot-starter-mail:3.4.6'
    implementation 'org.springframework:spring-context-support:6.2.7'
    compileOnly 'org.projectlombok:lombok:1.18.36'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.17.1'
    implementation 'net.sf.jasperreports:jasperreports:7.0.3'
    implementation 'net.sf.jasperreports:jasperreports-pdf:7.0.3'
    implementation 'net.sf.jasperreports:jasperreports-barcode4j:7.0.3'
    implementation 'net.sf.jasperreports:jasperreports-jdt:7.0.3'
    implementation 'net.sf.barcode4j:barcode4j:2.1'
    compileOnly 'org.projectlombok:lombok:1.18.36'
    runtimeOnly 'org.postgresql:postgresql:42.7.3'
    runtimeOnly 'com.h2database:h2:2.2.224'
    implementation(group: 'org.openapitools', name: 'openapi-spring', version: '1.14.7')
    annotationProcessor 'org.projectlombok:lombok:1.18.36'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
    testImplementation 'org.springframework.boot:spring-boot-starter-test:3.4.6'
    implementation 'org.kie:kie-dmn-core:10.0.0'
    implementation 'org.kie:kie-ci:10.0.0'
    testImplementation 'org.hamcrest:hamcrest:2.2'
    testImplementation 'org.springframework.security:spring-security-test:6.4.4'
    testImplementation "com.c4-soft.springaddons:spring-addons-oauth2-test:6.0.12"
    implementation 'org.springframework.boot:spring-boot-starter-actuator:3.4.6'
    implementation 'org.springframework.boot:spring-boot-starter-aop:3.4.6'
    implementation platform('io.micrometer:micrometer-bom:1.12.5')
    implementation 'io.micrometer:micrometer-tracing-bridge-otel:1.2.5'
    implementation 'io.micrometer:micrometer-tracing:1.2.5'
    implementation 'org.apache.commons:commons-csv:1.11.0'
    implementation 'org.springframework.boot:spring-boot-starter-webflux:3.4.6'
    implementation 'javax.validation:validation-api:2.0.0.Final'
    testImplementation 'io.projectreactor:reactor-test:3.1.0.RELEASE'
    implementation 'io.netty:netty-handler:4.1.118.Final'
    implementation 'io.netty:netty-common:4.1.118.Final'
    implementation 'net.minidev:json-smart:2.5.2'

    implementation 'com.amazonaws:aws-java-sdk-core:1.12.689'
    implementation 'com.amazonaws:aws-java-sdk-s3:1.12.689'

    constraints {
        implementation('org.bouncycastle:bcprov-jdk18on:1.78') {
            because "CVE-2024-29857, CVE-2024-30171, CVE-2024-30172, CVE-2024-34447"
        }
        implementation('com.thoughtworks.xstream:xstream:1.4.21') {
            because "CVE-2024-47072"
        }
        implementation('com.google.protobuf:protobuf-java:3.25.5') {
            because "CVE-2024-7254"
        }
        implementation('org.keycloak:keycloak-core:26.0.6') {
            because "CVE-2024-10039, CVE-2024-7318, CVE-2024-7260"
        }
        implementation("commons-beanutils:commons-beanutils:1.11.0") {
            because "CVE-2025-48734"
        }
    }
}

compileJava {
    options.compilerArgs = [
            '-Amapstruct.defaultComponentModel=default',
            '-Amapstruct.nullValueIterableMappingStrategy=RETURN_DEFAULT'
    ]
}

tasks.named('test') {
    enabled = false
}

test {
    systemProperty 'spring.profiles.active', 'unittest'
}

task unitTest(type: Test) {
    systemProperty 'spring.profiles.active', 'unittest'
    useJUnitPlatform {
        filter {
            excludeTestsMatching '*ApplicationTests'
            excludeTestsMatching '*IntegrationTest'
            failOnNoMatchingTests = false
        }
    }
}

task integrationTest(type: Test) {
    systemProperty 'spring.profiles.active', 'unittest, clean-db'
    useJUnitPlatform {
        filter {
            includeTestsMatching '*ApplicationTests'
            includeTestsMatching '*IntegrationTest'
            failOnNoMatchingTests = false
        }
    }
}

springBoot {
    buildInfo()
}

//bootJar {
////    enabled = false
//    mainClass = 'de.adesso.fischereiregister.RegisterServiceApplication'
//}

//jar {
//    enabled = true
//}

subprojects {
    apply plugin: 'java-library'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'

    java {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }

    bootJar {
        enabled = false
    }

    repositories {
        mavenLocal()
        maven {
            url 'https://artifactory.adesso-group.com/artifactory/fischereiregister-maven-release'
            credentials {
                username = artifactoryUser
                password = artifactoryToken
            }
        }
        maven {
            url 'https://repo-ex.zcdi.dataport.de:443/artifactory/digifischdok-mvn-dev'
            credentials {
                username = System.getenv("MAVEN_SDK_REGISTRY_USER_ID")
                password = System.getenv("MAVEN_SDK_REGISTRY_TOKEN")
            }
        }
        maven {
            url 'https://common-gradle-gradle-remote.repo-in.zcdi.dataport.de/artifactory/common-gradle-gradle-remote'
            credentials {
                username = System.getenv("GRADLE_REGISTRY_USER_ID")
                password = System.getenv("GRADLE_REGISTRY_TOKEN")
            }
        }
        mavenCentral()
        maven { url 'https://mvnrepository.com' }
    }

    compileJava {
        options.compilerArgs = [
                '-Amapstruct.defaultComponentModel=spring'
        ]
    }

    test {
        systemProperty 'spring.profiles.active', 'unittest'
        useJUnitPlatform()
    }

    task unitTest(type: Test) {
        systemProperty 'spring.profiles.active', 'unittest'
        useJUnitPlatform {
            filter {
                excludeTestsMatching '*ApplicationTests'
                excludeTestsMatching '*IntegrationTest'
                failOnNoMatchingTests = false
            }
        }
    }

    task integrationTest(type: Test) {
        systemProperty 'spring.profiles.active', 'unittest'
        useJUnitPlatform {
            filter {
                includeTestsMatching '*ApplicationTests'
                includeTestsMatching '*IntegrationTest'
                failOnNoMatchingTests = false
            }
        }
    }
}

allprojects {
    apply plugin: 'jacoco'

    jacoco {
        toolVersion = "0.8.12"
    }

    tasks.jacocoTestReport {
        dependsOn unitTest
        reports {
            xml.required = true
            csv.required = false
            html.required = true
        }
        executionData files(tasks.named('unitTest')).filter { it.name.endsWith('.exec') && it.exists() } // ??
    }

    // Exclude integration tests from build
    check.dependsOn -= integrationTest
}

task jacocoFullReport(type: JacocoReport) {
    dependsOn = allprojects.unitTest

    additionalSourceDirs.from = files(allprojects.sourceSets.main.allSource.srcDirs)
    sourceDirectories.from = files(allprojects.sourceSets.main.allSource.srcDirs)
    classDirectories.from = files(allprojects.sourceSets.main.output)
    executionData.from = files(allprojects.jacocoTestReport.executionData)

    reports {
        html.required = true
        xml.required = true
        csv.required = false
    }
}